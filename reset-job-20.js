// Load environment variables
require('dotenv').config();

const { getDatabase } = require('./lib/database');

async function resetJob20() {
  console.log('🔄 Resetting Job 20...\n');
  
  try {
    const db = getDatabase();
    
    // Check current status
    const job20 = db.db.prepare('SELECT * FROM queue_jobs WHERE id = ?').get(20);
    
    if (!job20) {
      console.log('❌ Job 20 not found');
      return;
    }
    
    console.log('📋 Current Job 20 Status:');
    console.log(`   Status: ${job20.status}`);
    console.log(`   Retry Count: ${job20.retry_count}`);
    console.log(`   Max Retries: ${job20.max_retries}`);
    console.log(`   Error: ${job20.error_message || 'None'}`);
    
    // Reset the job
    const resetStmt = db.db.prepare(`
      UPDATE queue_jobs
      SET status = 'queued',
          retry_count = 0,
          error_message = NULL
      WHERE id = ?
    `);
    
    const result = resetStmt.run(20);
    
    if (result.changes > 0) {
      console.log('✅ Job 20 reset successfully');
      
      // Check new status
      const updatedJob = db.db.prepare('SELECT * FROM queue_jobs WHERE id = ?').get(20);
      console.log('\n📋 Updated Job 20 Status:');
      console.log(`   Status: ${updatedJob.status}`);
      console.log(`   Retry Count: ${updatedJob.retry_count}`);
      console.log(`   Max Retries: ${updatedJob.max_retries}`);
      console.log(`   Error: ${updatedJob.error_message || 'None'}`);
      console.log(`   Updated: Job reset successfully`);
      
      console.log('\n🎉 Job 20 is now ready for processing with the fixed authentication!');
      console.log('💡 The queue processor should pick it up automatically.');
      
    } else {
      console.log('❌ Failed to reset job 20');
    }
    
  } catch (error) {
    console.error('❌ Error resetting job 20:', error);
  }
}

if (require.main === module) {
  resetJob20();
}

module.exports = { resetJob20 };
