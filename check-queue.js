// Load environment variables
require('dotenv').config();

const { getDatabase } = require('./lib/database');

async function checkQueue() {
  console.log('🔍 Checking Queue Status...\n');
  
  try {
    const db = getDatabase();
    
    // Check pending jobs
    console.log('📋 Checking pending jobs...');
    const pendingJobs = db.db.prepare(`
      SELECT j.*, u.username 
      FROM queue_jobs j 
      JOIN users u ON j.user_id = u.id 
      WHERE j.status = 'pending' 
      ORDER BY j.created_at DESC
    `).all();
    
    console.log(`Found ${pendingJobs.length} pending jobs:`);
    pendingJobs.forEach((job, i) => {
      console.log(`  ${i + 1}. Job ID: ${job.id}, User: ${job.username}, Type: ${job.job_type}, Created: ${job.created_at}`);
    });
    
    // Check failed jobs
    console.log('\n❌ Checking failed jobs...');
    const failedJobs = db.db.prepare(`
      SELECT j.*, u.username
      FROM queue_jobs j
      JOIN users u ON j.user_id = u.id
      WHERE j.status = 'failed'
      ORDER BY j.created_at DESC
      LIMIT 10
    `).all();
    
    console.log(`Found ${failedJobs.length} recent failed jobs:`);
    failedJobs.forEach((job, i) => {
      console.log(`  ${i + 1}. Job ID: ${job.id}, User: ${job.username}, Error: ${job.error_message}, Created: ${job.created_at}`);
    });

    // Check processing jobs
    console.log('\n⚙️ Checking processing jobs...');
    const processingJobs = db.db.prepare(`
      SELECT j.*, u.username
      FROM queue_jobs j
      JOIN users u ON j.user_id = u.id
      WHERE j.status = 'processing'
      ORDER BY j.created_at DESC
    `).all();

    console.log(`Found ${processingJobs.length} processing jobs:`);
    processingJobs.forEach((job, i) => {
      console.log(`  ${i + 1}. Job ID: ${job.id}, User: ${job.username}, Started: ${job.created_at}`);
    });

    // Check recent completed jobs
    console.log('\n✅ Checking recent completed jobs...');
    const completedJobs = db.db.prepare(`
      SELECT j.*, u.username
      FROM queue_jobs j
      JOIN users u ON j.user_id = u.id
      WHERE j.status = 'completed'
      ORDER BY j.created_at DESC
      LIMIT 5
    `).all();

    console.log(`Found ${completedJobs.length} recent completed jobs:`);
    completedJobs.forEach((job, i) => {
      console.log(`  ${i + 1}. Job ID: ${job.id}, User: ${job.username}, Completed: ${job.created_at}`);
    });
    
    // Check queue processor health
    console.log('\n🏥 Checking queue processor health...');
    const healthMetrics = db.db.prepare(`
      SELECT * FROM system_health 
      ORDER BY last_updated DESC 
      LIMIT 1
    `).get();
    
    if (healthMetrics) {
      console.log('Health metrics:', healthMetrics);
    } else {
      console.log('No health metrics found');
    }
    
    // Check if there are any jobs with specific error patterns
    console.log('\n🔍 Checking for authentication-related errors...');
    const authErrors = db.db.prepare(`
      SELECT j.*, u.username
      FROM queue_jobs j
      JOIN users u ON j.user_id = u.id
      WHERE j.error_message LIKE '%Authentication%' OR j.error_message LIKE '%Login failed%'
      ORDER BY j.created_at DESC
      LIMIT 10
    `).all();

    console.log(`Found ${authErrors.length} authentication-related errors:`);
    authErrors.forEach((job, i) => {
      console.log(`  ${i + 1}. Job ID: ${job.id}, User: ${job.username}, Error: ${job.error_message}`);
      console.log(`      Retry Count: ${job.retry_count}, Created: ${job.created_at}`);
    });
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

if (require.main === module) {
  checkQueue();
}

module.exports = { checkQueue };
