// Load environment variables
require('dotenv').config();

const { getAuthManager } = require('./lib/auth');
const { getDatabase } = require('./lib/database');

async function testApiCall() {
  console.log('🧪 Testing API Call with Real Authentication...\n');
  
  try {
    const db = getDatabase();
    const auth = getAuthManager();
    
    // Get a test user
    const testUser = db.db.prepare('SELECT * FROM users WHERE username = ? AND is_active = 1').get('STAXZY');
    if (!testUser) {
      console.log('❌ Test user not found');
      return;
    }
    
    console.log(`✅ Using test user: ${testUser.username} (ID: ${testUser.id})`);
    
    // Create session exactly like queue processor
    const tempSession = auth.createSession(testUser, 'queue-processor', 'Internal Queue Processor');
    console.log(`✅ Created session: ${tempSession.token.substring(0, 30)}...`);
    
    // Test the API call
    console.log('\n🔗 Making API call to /api/sparxreader/start...');
    
    const response = await fetch('http://localhost:3000/api/sparxreader/start', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${tempSession.token}`
      },
      body: JSON.stringify({
        url: 'https://selectschool.sparxmaths.uk/?app=sparx_reader',
        targetSrp: 100,
        credentials: {
          school: 'test-school',
          email: '<EMAIL>',
          password: 'test-password'
        }
      })
    });
    
    console.log(`📊 Response Status: ${response.status}`);
    console.log(`📊 Response Headers:`, Object.fromEntries(response.headers.entries()));
    
    let responseData;
    try {
      responseData = await response.json();
      console.log(`📊 Response Data:`, responseData);
    } catch (jsonError) {
      console.log(`❌ Failed to parse JSON response:`, jsonError.message);
      const textResponse = await response.text();
      console.log(`📊 Raw Response:`, textResponse);
    }
    
    // Clean up
    auth.logout(tempSession.token, testUser.id);
    console.log('\n✅ Session cleaned up');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Check if server is running first
async function checkServer() {
  try {
    const response = await fetch('http://localhost:3000/api/auth/validate', {
      method: 'GET'
    });
    return response.status !== undefined;
  } catch (error) {
    return false;
  }
}

async function main() {
  const serverRunning = await checkServer();
  if (!serverRunning) {
    console.log('❌ Server is not running on localhost:3000');
    console.log('💡 Please start the server with: npm run dev');
    return;
  }
  
  console.log('✅ Server is running');
  await testApiCall();
}

if (require.main === module) {
  main();
}

module.exports = { testApiCall };
