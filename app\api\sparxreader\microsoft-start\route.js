import { NextResponse } from 'next/server';
import { chromium } from 'playwright';
import path from 'path';
const { setGlobalBrowser, getGlobalBrowser, getGlobalPage, clearGlobalBrowser } = require('../browser-context.js');
const { getAuthManager } = require('../../../../lib/auth');
const { getDatabase } = require('../../../../lib/database');

// Authentication and validation middleware
async function withAuth(request, handler) {
  try {
    const auth = getAuthManager();

    // Get token from header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    const token = authHeader.substring(7);

    // Validate session - this can throw an error
    let session;
    try {
      session = auth.validateSession(token);
    } catch (validationError) {
      console.error('Session validation failed:', validationError.message);
      return NextResponse.json({ error: 'Authentication failed' }, { status: 401 });
    }

    if (!session) {
      return NextResponse.json({ error: 'Invalid session' }, { status: 401 });
    }

    // Check daily main job limit
    const db = getDatabase();
    const mainJobLimitConfig = db.getSystemConfig('default_max_main_jobs_per_day');
    const maxMainJobsPerDay = mainJobLimitConfig ? mainJobLimitConfig.config_value : 5;

    const today = new Date().toISOString().split('T')[0];
    const mainJobCountStmt = db.db.prepare(`
      SELECT COUNT(*) as count
      FROM queue_jobs
      WHERE user_id = ? AND DATE(created_at) = ? AND batch_id IS NULL
    `);
    const mainJobCount = mainJobCountStmt.get(session.userId, today);

    if (mainJobCount.count >= maxMainJobsPerDay) {
      return NextResponse.json({
        error: 'Daily main job limit reached',
        current_count: mainJobCount.count,
        max_allowed: maxMainJobsPerDay,
        message: `You have reached your daily limit of ${maxMainJobsPerDay} main automation job${maxMainJobsPerDay > 1 ? 's' : ''}. Please try again tomorrow.`
      }, { status: 429 });
    }

    return handler(request, session);
  } catch (error) {
    console.error('Auth error:', error);
    return NextResponse.json({ error: 'Authentication failed' }, { status: 401 });
  }
}

export async function POST(request) {
  return withAuth(request, async (request, user) => {
  try {
    const { url, targetSrp, credentials } = await request.json();
    
    const extensionPath = path.resolve(process.cwd(), 'Sparxext reader');
    
    try {
      // Always create new browser session for first try
      const existingBrowser = getGlobalBrowser();
      
      // Close any existing browser session
      if (existingBrowser) {
        try {
          await existingBrowser.close();
          console.log('Closed existing browser session');
        } catch (error) {
          console.log('Error closing existing browser:', error.message);
        }
      }
      
      // Create new browser session
      console.log('Creating new browser session...');
      
      const browser = await chromium.launchPersistentContext('', {
        headless: false,
        args: [
          `--disable-extensions-except=${extensionPath}`,
          `--load-extension=${extensionPath}`,
          '--no-sandbox',
          '--disable-setuid-sandbox'
        ]
      });
      
      const page = await browser.newPage();
      
      // Store globally for use in other endpoints
      setGlobalBrowser(browser, page);
      
    
    console.log('Navigating to Sparx Learning...');
    await page.goto(url, { timeout: 15000 });

    console.log('Selecting school');
    try {
        // Use school from credentials
        if (!credentials || !credentials.school) {
          throw new Error('School name is required');
        }
        
        await page.type('input[type="text"], input[type="search"], input', credentials.school);
        await page.press('input[type="text"], input[type="search"], input', 'Enter');
        await page.waitForTimeout(1000);
        
        try {
          await page.click('button:has-text("Continue")', { timeout: 5000 });
        } catch (error) {
          console.log('Continue button not found, proceeding anyway');
        }
        
        await page.waitForTimeout(2000);
        
        try {
          await page.click('div#cookiescript_accept', { timeout: 5000 });
        } catch (error) {
          console.log('Cookie accept button not found, proceeding anyway');
        }
    } catch (schoolError) {
        console.log('School selection error:', schoolError.message);
    }
    
    await page.waitForTimeout(3000);
    
    // Click the Microsoft SSO login button
    const microsoftButton = page.locator('div.sso-login-button[onclick*="oauth2/login"]');
    
    if (await microsoftButton.count() > 0) {
      console.log('Found Microsoft login button, clicking...');
      await microsoftButton.click();
    } else {
      console.log('Microsoft login button not found, trying alternative selectors...');
      const altSelectors = [
        'a[href*="microsoftonline.com"]',
        'button:has-text("Microsoft")',
        '[data-provider="microsoft"]',
        '.microsoft-login'
      ];
      
      let found = false;
      for (const selector of altSelectors) {
        const element = page.locator(selector);
        if (await element.count() > 0) {
          console.log(`Found Microsoft login with selector: ${selector}`);
          await element.click();
          found = true;
          break;
        }
      }
      
      if (!found) {
        throw new Error('Microsoft login button not found');
      }
    }
    
    await page.waitForTimeout(3000);
    
    // Input email address
    console.log('Inputting email address...');
    
    // Use credentials from request
    if (!credentials || !credentials.email || !credentials.password) {
      throw new Error('Login credentials are required');
    }
    
    await page.type('input[type="email"], input[name="loginfmt"], input[placeholder*="email"]', credentials.email);
    
    // Wait and click Next button
    await page.waitForTimeout(1000);
    console.log('Clicking Next button...');
    await page.click('button:has-text("Next"), input[type="submit"][value="Next"], button[id*="next"]');
    
    // Wait and input password
    await page.waitForTimeout(4000);
    console.log('Inputting password...');
    await page.type('input[type="password"], input[name="passwd"], input[placeholder*="password"]', credentials.password);
    
    // Wait and click Sign in button
    await page.waitForTimeout(1000);
    console.log('Clicking Sign in button...');
    await page.click('button:has-text("Sign in"), input[type="submit"][value="Sign in"], button[id*="signin"]');
    
    // Wait and click No button
    await page.waitForTimeout(2000);
    console.log('Clicking No button...');
    await page.click('button:has-text("No"), input[type="button"][value="No"], button[id*="no"]');
    
    // Wait for login completion and navigate to library
    console.log('Waiting for login completion...');
    await page.waitForTimeout(5000);
    
    console.log('Navigating to Sparx Reader library page...');
    await page.goto('https://reader.sparx-learning.com/library', { 
      waitUntil: 'domcontentloaded',
      timeout: 30000 
    });
    
    await page.waitForTimeout(5000);
    
    // Extract user's total SRP using enhanced function from start route
    console.log('Extracting user total SRP...');
    let userTotalSrp = null;
    
    // Try to get user total SRP with multiple attempts
    for (let attempt = 0; attempt < 5; attempt++) {
      try {
        userTotalSrp = await page.evaluate(() => {
          // Get the user's total SRP
          const userTotalSrpElement = document.querySelector('.sr_92b39de6');
          return userTotalSrpElement ? userTotalSrpElement.textContent.replace(/[^\d,]/g, '').replace(',', '') : null;
        });
        
        if (userTotalSrp) {
          console.log(`User Total SRP extracted on attempt ${attempt + 1}: ${userTotalSrp}`);
          break;
        }
        
        // If we didn't get the info, wait a short time and try again
        if (attempt < 4) {
          await page.waitForTimeout(200);
        }
      } catch (error) {
        console.log(`SRP extraction attempt ${attempt + 1} failed:`, error.message);
        if (attempt < 4) {
          await page.waitForTimeout(200);
        }
      }
    }
    
    // Store initial SRP info everywhere im pretty sure and in browser localStorage
    global.sessionSrpInfo = { 
      initialUserTotalSrp: userTotalSrp,
      targetSrpNeeded: targetSrp || null
    };
    
    // Store target SRP and initial SRP in browser localStorage so extension can do its thang
    if (targetSrp) {
      await page.evaluate(({target, initial}) => {
        localStorage.setItem('targetSrp', target.toString());
        localStorage.setItem('initialSrp', initial || '0');
      }, {target: targetSrp, initial: userTotalSrp});
      console.log(`Target SRP stored in localStorage: ${targetSrp}`);
      console.log(`Initial SRP stored in localStorage: ${userTotalSrp}`);
    }
    
    console.log('Microsoft login setup complete.');
    
    return NextResponse.json({
      success: true,
      currentSrp: userTotalSrp || '0',
      bookTitle: 'Microsoft Login - Ready',
      message: 'Microsoft login initiated successfully'
    });
    
    } catch (playwrightError) {
      if (playwrightError.message.includes("Executable doesn't exist")) {
        return NextResponse.json({ 
          success: false, 
          error: "Playwright browsers not installed. Please run 'npx playwright install chromium' in your terminal.",
          needsPlaywright: true
        }, { status: 500 });
      } else {
        throw playwrightError;
      }
    }
  } catch (error) {
    console.error('Error opening Sparx Reader with extension:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
  });
}