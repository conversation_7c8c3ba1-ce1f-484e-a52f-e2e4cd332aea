const { getDatabase } = require('./lib/database');

// Reset job 17 for testing
const db = getDatabase();

console.log('Resetting job 17...');

// Reset retry count and status
const resetStmt = db.db.prepare(`
  UPDATE queue_jobs 
  SET retry_count = 0, status = 'queued', error_message = NULL
  WHERE id = 17
`);

const result = resetStmt.run();

if (result.changes > 0) {
  console.log('✅ Job 17 reset successfully');
  
  // Check the job status
  const checkStmt = db.db.prepare('SELECT * FROM queue_jobs WHERE id = 17');
  const job = checkStmt.get();
  console.log('Job 17 status:', {
    id: job.id,
    status: job.status,
    retry_count: job.retry_count,
    max_retries: job.max_retries,
    error_message: job.error_message
  });
} else {
  console.log('❌ Failed to reset job 17');
}
