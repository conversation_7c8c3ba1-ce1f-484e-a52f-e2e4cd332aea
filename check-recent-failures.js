// Load environment variables
require('dotenv').config();

const { getDatabase } = require('./lib/database');

async function checkRecentFailures() {
  console.log('🔍 Checking Recent Authentication Failures...\n');
  
  try {
    const db = getDatabase();
    
    // Check all recent failed jobs with details
    console.log('❌ Checking all recent failed jobs...');
    const failedJobs = db.db.prepare(`
      SELECT j.*, u.username 
      FROM queue_jobs j 
      JOIN users u ON j.user_id = u.id 
      WHERE j.status = 'failed' 
      ORDER BY j.created_at DESC 
      LIMIT 20
    `).all();
    
    console.log(`Found ${failedJobs.length} recent failed jobs:`);
    failedJobs.forEach((job, i) => {
      console.log(`\n${i + 1}. Job ID: ${job.id}`);
      console.log(`   User: ${job.username} (ID: ${job.user_id})`);
      console.log(`   Type: ${job.job_type}`);
      console.log(`   Status: ${job.status}`);
      console.log(`   Error: ${job.error_message || 'No error message'}`);
      console.log(`   Retry Count: ${job.retry_count}`);
      console.log(`   Created: ${job.created_at}`);
      console.log(`   Job Data: ${job.job_data}`);
    });
    
    // Check for authentication-specific errors
    console.log('\n🔍 Checking for authentication-related errors...');
    const authErrors = db.db.prepare(`
      SELECT j.*, u.username 
      FROM queue_jobs j 
      JOIN users u ON j.user_id = u.id 
      WHERE (j.error_message LIKE '%Authentication%' OR j.error_message LIKE '%Login failed%' OR j.error_message LIKE '%Invalid%')
      ORDER BY j.created_at DESC 
      LIMIT 10
    `).all();
    
    console.log(`Found ${authErrors.length} authentication-related errors:`);
    authErrors.forEach((job, i) => {
      console.log(`\n${i + 1}. Job ID: ${job.id}`);
      console.log(`   User: ${job.username}`);
      console.log(`   Error: ${job.error_message}`);
      console.log(`   Retry Count: ${job.retry_count}`);
      console.log(`   Created: ${job.created_at}`);
    });
    
    // Check the most recent jobs regardless of status
    console.log('\n📊 Checking most recent jobs (all statuses)...');
    const recentJobs = db.db.prepare(`
      SELECT j.*, u.username 
      FROM queue_jobs j 
      JOIN users u ON j.user_id = u.id 
      ORDER BY j.id DESC 
      LIMIT 10
    `).all();
    
    console.log(`Found ${recentJobs.length} recent jobs:`);
    recentJobs.forEach((job, i) => {
      console.log(`${i + 1}. Job ID: ${job.id}, User: ${job.username}, Status: ${job.status}, Created: ${job.created_at}`);
      if (job.error_message) {
        console.log(`   Error: ${job.error_message}`);
      }
    });
    
    // Check if there are any jobs from today
    console.log('\n📅 Checking jobs from today...');
    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
    const todayJobs = db.db.prepare(`
      SELECT j.*, u.username 
      FROM queue_jobs j 
      JOIN users u ON j.user_id = u.id 
      WHERE j.created_at LIKE ?
      ORDER BY j.created_at DESC
    `).all(`${today}%`);
    
    console.log(`Found ${todayJobs.length} jobs from today:`);
    todayJobs.forEach((job, i) => {
      console.log(`${i + 1}. Job ID: ${job.id}, User: ${job.username}, Status: ${job.status}, Error: ${job.error_message || 'None'}`);
    });
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

if (require.main === module) {
  checkRecentFailures();
}

module.exports = { checkRecentFailures };
