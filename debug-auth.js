// Load environment variables
require('dotenv').config();

const { getDatabase } = require('./lib/database');
const { getAuthManager } = require('./lib/auth');

async function debugAuth() {
  console.log('🔍 Debugging Authentication Flow...\n');
  
  try {
    const db = getDatabase();
    const auth = getAuthManager();
    
    // Check if we have any users
    console.log('📊 Checking users in database...');
    const users = db.db.prepare('SELECT id, username, role, is_active FROM users ORDER BY id').all();
    console.log(`Found ${users.length} users:`);
    users.forEach(user => {
      console.log(`  - ID: ${user.id}, Username: ${user.username}, Role: ${user.role}, Active: ${user.is_active}`);
    });
    
    if (users.length === 0) {
      console.log('❌ No users found! Creating default admin...');
      db.createDefaultAdmin();
      
      // Re-check users
      const newUsers = db.db.prepare('SELECT id, username, role, is_active FROM users ORDER BY id').all();
      console.log(`After creating admin, found ${newUsers.length} users:`);
      newUsers.forEach(user => {
        console.log(`  - ID: ${user.id}, Username: ${user.username}, Role: ${user.role}, Active: ${user.is_active}`);
      });
    }
    
    // Test with the first user (should be admin)
    const testUser = users[0] || db.db.prepare('SELECT * FROM users ORDER BY id LIMIT 1').get();
    
    if (!testUser) {
      console.log('❌ Still no users found after creating admin!');
      return;
    }
    
    console.log(`\n🧪 Testing authentication flow with user: ${testUser.username} (ID: ${testUser.id})`);
    
    // Step 1: Create a session (simulating queue processor)
    console.log('\n1️⃣ Creating temporary session...');
    const tempSession = auth.createSession(testUser, 'queue-processor', 'Internal Queue Processor');
    console.log(`✅ Session created successfully`);
    console.log(`   Token: ${tempSession.token.substring(0, 30)}...`);
    console.log(`   Expires: ${tempSession.expiresAt}`);
    
    // Step 2: Validate the session immediately
    console.log('\n2️⃣ Validating session immediately...');
    try {
      const validationResult = auth.validateSession(tempSession.token);
      console.log('✅ Session validation successful:', validationResult);
    } catch (validationError) {
      console.log('❌ Session validation failed:', validationError.message);
      
      // Debug the validation process
      console.log('\n🔍 Debugging validation process...');
      
      // Check JWT verification
      try {
        const decoded = auth.verifyToken(tempSession.token);
        console.log('✅ JWT verification successful:', decoded);
      } catch (jwtError) {
        console.log('❌ JWT verification failed:', jwtError.message);
        return;
      }
      
      // Check database session
      const tokenHash = auth.hashToken(tempSession.token);
      console.log(`Token hash: ${tokenHash.substring(0, 30)}...`);
      
      const dbSession = db.validateSession(tokenHash);
      if (dbSession) {
        console.log('✅ Database session found:', dbSession);
      } else {
        console.log('❌ Database session not found');
        
        // Check all sessions for this user
        const allSessions = db.db.prepare(`
          SELECT s.*, u.username 
          FROM user_sessions s 
          JOIN users u ON s.user_id = u.id 
          WHERE s.user_id = ? 
          ORDER BY s.created_at DESC
        `).all(testUser.id);
        
        console.log(`Found ${allSessions.length} sessions for user ${testUser.username}:`);
        allSessions.forEach((session, i) => {
          console.log(`  ${i + 1}. Hash: ${session.token_hash.substring(0, 20)}... Active: ${session.is_active} Expires: ${session.expires_at}`);
        });
      }
    }
    
    // Step 3: Test the exact queue processor flow
    console.log('\n3️⃣ Testing exact queue processor flow...');

    // Simulate the exact same flow as queue processor
    console.log('Creating session exactly like queue processor...');
    const queueSession = auth.createSession(testUser, 'queue-processor', 'Internal Queue Processor');
    console.log(`Queue session token: ${queueSession.token.substring(0, 30)}...`);

    // Immediately try to validate it (like the API endpoint does)
    console.log('Immediately validating session...');
    try {
      const immediateValidation = auth.validateSession(queueSession.token);
      console.log('✅ Immediate validation successful:', immediateValidation);
    } catch (immediateError) {
      console.log('❌ Immediate validation failed:', immediateError.message);

      // Debug the immediate failure
      console.log('\n🔍 Debugging immediate validation failure...');

      // Check JWT
      try {
        const decoded = auth.verifyToken(queueSession.token);
        console.log('✅ JWT verification successful:', decoded);
      } catch (jwtError) {
        console.log('❌ JWT verification failed:', jwtError.message);
      }

      // Check database
      const tokenHash = auth.hashToken(queueSession.token);
      console.log(`Token hash: ${tokenHash.substring(0, 30)}...`);

      const dbSession = db.validateSession(tokenHash);
      console.log('Database session result:', dbSession);

      // Check if session exists but with different criteria
      const rawSession = db.db.prepare(`
        SELECT s.*, u.username, u.role, u.is_active as user_active
        FROM user_sessions s
        JOIN users u ON s.user_id = u.id
        WHERE s.token_hash = ?
      `).get(tokenHash);
      console.log('Raw session (ignoring active/expiry):', rawSession);
    }

    // Step 4: Test the actual API endpoint with the queue session
    console.log('\n4️⃣ Testing API endpoint with queue session...');
    try {
      const response = await fetch('http://localhost:3000/api/sparxreader/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${queueSession.token}`
        },
        body: JSON.stringify({
          url: 'https://selectschool.sparxmaths.uk/?app=sparx_reader',
          targetSrp: 100,
          credentials: {
            school: 'test-school',
            email: '<EMAIL>',
            password: 'test-password'
          }
        })
      });

      console.log(`✅ API Response status: ${response.status}`);
      const responseData = await response.json();
      console.log('✅ API Response data:', responseData);

    } catch (fetchError) {
      console.log('❌ API call failed:', fetchError.message);
    }

    // Clean up queue session
    try {
      auth.logout(queueSession.token, testUser.id);
      console.log('✅ Queue session cleaned up');
    } catch (cleanupError) {
      console.log('⚠️ Queue session cleanup warning:', cleanupError.message);
    }
    
    // Clean up
    console.log('\n🧹 Cleaning up...');
    try {
      auth.logout(tempSession.token, testUser.id);
      console.log('✅ Session cleaned up successfully');
    } catch (cleanupError) {
      console.log('⚠️ Cleanup warning:', cleanupError.message);
    }
    
  } catch (error) {
    console.error('❌ Debug error:', error);
  }
}

if (require.main === module) {
  debugAuth();
}

module.exports = { debugAuth };
