// Load environment variables
require('dotenv').config();

const { getDatabase } = require('./lib/database');
const { getAuthManager } = require('./lib/auth');

async function debugAuth() {
  console.log('🔍 Debugging Authentication Flow...\n');
  
  try {
    const db = getDatabase();
    const auth = getAuthManager();
    
    // Check if we have any users
    console.log('📊 Checking users in database...');
    const users = db.db.prepare('SELECT id, username, role, is_active FROM users ORDER BY id').all();
    console.log(`Found ${users.length} users:`);
    users.forEach(user => {
      console.log(`  - ID: ${user.id}, Username: ${user.username}, Role: ${user.role}, Active: ${user.is_active}`);
    });
    
    if (users.length === 0) {
      console.log('❌ No users found! Creating default admin...');
      db.createDefaultAdmin();
      
      // Re-check users
      const newUsers = db.db.prepare('SELECT id, username, role, is_active FROM users ORDER BY id').all();
      console.log(`After creating admin, found ${newUsers.length} users:`);
      newUsers.forEach(user => {
        console.log(`  - ID: ${user.id}, Username: ${user.username}, Role: ${user.role}, Active: ${user.is_active}`);
      });
    }
    
    // Test with the first user (should be admin)
    const testUser = users[0] || db.db.prepare('SELECT * FROM users ORDER BY id LIMIT 1').get();
    
    if (!testUser) {
      console.log('❌ Still no users found after creating admin!');
      return;
    }
    
    console.log(`\n🧪 Testing authentication flow with user: ${testUser.username} (ID: ${testUser.id})`);
    
    // Step 1: Create a session (simulating queue processor)
    console.log('\n1️⃣ Creating temporary session...');
    const tempSession = auth.createSession(testUser, 'queue-processor', 'Internal Queue Processor');
    console.log(`✅ Session created successfully`);
    console.log(`   Token: ${tempSession.token.substring(0, 30)}...`);
    console.log(`   Expires: ${tempSession.expiresAt}`);
    
    // Step 2: Validate the session immediately
    console.log('\n2️⃣ Validating session immediately...');
    try {
      const validationResult = auth.validateSession(tempSession.token);
      console.log('✅ Session validation successful:', validationResult);
    } catch (validationError) {
      console.log('❌ Session validation failed:', validationError.message);
      
      // Debug the validation process
      console.log('\n🔍 Debugging validation process...');
      
      // Check JWT verification
      try {
        const decoded = auth.verifyToken(tempSession.token);
        console.log('✅ JWT verification successful:', decoded);
      } catch (jwtError) {
        console.log('❌ JWT verification failed:', jwtError.message);
        return;
      }
      
      // Check database session
      const tokenHash = auth.hashToken(tempSession.token);
      console.log(`Token hash: ${tokenHash.substring(0, 30)}...`);
      
      const dbSession = db.validateSession(tokenHash);
      if (dbSession) {
        console.log('✅ Database session found:', dbSession);
      } else {
        console.log('❌ Database session not found');
        
        // Check all sessions for this user
        const allSessions = db.db.prepare(`
          SELECT s.*, u.username 
          FROM user_sessions s 
          JOIN users u ON s.user_id = u.id 
          WHERE s.user_id = ? 
          ORDER BY s.created_at DESC
        `).all(testUser.id);
        
        console.log(`Found ${allSessions.length} sessions for user ${testUser.username}:`);
        allSessions.forEach((session, i) => {
          console.log(`  ${i + 1}. Hash: ${session.token_hash.substring(0, 20)}... Active: ${session.is_active} Expires: ${session.expires_at}`);
        });
      }
    }
    
    // Step 3: Test token hash consistency
    console.log('\n3️⃣ Testing token hash consistency...');
    const tokenHash1 = auth.hashToken(tempSession.token);
    const tokenHash2 = auth.hashToken(tempSession.token);
    console.log(`Hash 1: ${tokenHash1.substring(0, 30)}...`);
    console.log(`Hash 2: ${tokenHash2.substring(0, 30)}...`);
    console.log(`Hashes match: ${tokenHash1 === tokenHash2}`);

    // Step 4: Check session expiry
    console.log('\n4️⃣ Checking session expiry...');
    const sessionFromDb = db.db.prepare(`
      SELECT * FROM user_sessions
      WHERE token_hash = ? AND user_id = ?
    `).get(tokenHash1, testUser.id);

    if (sessionFromDb) {
      console.log(`Session expires at: ${sessionFromDb.expires_at}`);
      console.log(`Current time: ${new Date().toISOString()}`);
      console.log(`Is expired: ${new Date(sessionFromDb.expires_at) < new Date()}`);
    } else {
      console.log('❌ Session not found in database');
    }
    
    // Clean up
    console.log('\n🧹 Cleaning up...');
    try {
      auth.logout(tempSession.token, testUser.id);
      console.log('✅ Session cleaned up successfully');
    } catch (cleanupError) {
      console.log('⚠️ Cleanup warning:', cleanupError.message);
    }
    
  } catch (error) {
    console.error('❌ Debug error:', error);
  }
}

if (require.main === module) {
  debugAuth();
}

module.exports = { debugAuth };
