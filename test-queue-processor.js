// Load environment variables
require('dotenv').config();

const { getDatabase } = require('./lib/database');

async function testQueueProcessor() {
  console.log('🔍 Testing Queue Processor Logic...\n');
  
  try {
    const db = getDatabase();
    
    // Check if there are queued jobs
    console.log('📋 Checking queued jobs...');
    const queuedJobs = db.db.prepare(`
      SELECT qj.*, qb.batch_name, u.username
      FROM queue_jobs qj
      LEFT JOIN queue_batches qb ON qj.batch_id = qb.id
      JOIN users u ON qj.user_id = u.id
      WHERE qj.status = 'queued'
      AND (qj.scheduled_time IS NULL OR qj.scheduled_time <= datetime('now'))
      AND qj.retry_count < qj.max_retries
      ORDER BY qj.effective_priority DESC, qj.created_at ASC
      LIMIT 10
    `).all();
    
    console.log(`Found ${queuedJobs.length} queued jobs ready for processing:`);
    queuedJobs.forEach((job, i) => {
      console.log(`\n${i + 1}. Job ID: ${job.id}`);
      console.log(`   User: ${job.username} (ID: ${job.user_id})`);
      console.log(`   Type: ${job.job_type}`);
      console.log(`   Priority: ${job.effective_priority}`);
      console.log(`   Retry Count: ${job.retry_count}/${job.max_retries}`);
      console.log(`   Created: ${job.created_at}`);
      console.log(`   Scheduled: ${job.scheduled_time || 'Immediate'}`);
      console.log(`   Job Data: ${job.job_data}`);
    });
    
    if (queuedJobs.length > 0) {
      console.log('\n✅ There are jobs ready to be processed!');
      console.log('🤔 The queue processor should be picking these up...');
      
      // Check if queue processor is enabled
      console.log('\n🔧 Checking queue processor configuration...');
      console.log(`QUEUE_PROCESSOR_ENABLED: ${process.env.QUEUE_PROCESSOR_ENABLED}`);
      console.log(`QUEUE_PROCESSOR_INTERVAL: ${process.env.QUEUE_PROCESSOR_INTERVAL}`);
      console.log(`QUEUE_PROCESSOR_CONCURRENT_JOBS: ${process.env.QUEUE_PROCESSOR_CONCURRENT_JOBS}`);
      
      // Test if we can create a temporary session for the first job
      if (queuedJobs.length > 0) {
        const testJob = queuedJobs[0];
        console.log(`\n🧪 Testing session creation for job ${testJob.id}...`);
        
        const { getAuthManager } = require('./lib/auth');
        const auth = getAuthManager();
        
        try {
          const user = db.db.prepare('SELECT * FROM users WHERE id = ?').get(testJob.user_id);
          if (user) {
            console.log(`✅ User found: ${user.username}`);
            
            const tempSession = auth.createSession(user, 'queue-processor', 'Internal Queue Processor');
            console.log(`✅ Temporary session created successfully`);
            console.log(`   Token: ${tempSession.token.substring(0, 30)}...`);
            
            // Validate the session
            const validationResult = auth.validateSession(tempSession.token);
            console.log(`✅ Session validation successful:`, validationResult);
            
            // Clean up
            auth.logout(tempSession.token, user.id);
            console.log(`✅ Session cleaned up`);
            
          } else {
            console.log(`❌ User not found for job ${testJob.id}`);
          }
        } catch (sessionError) {
          console.log(`❌ Session test failed:`, sessionError.message);
        }
      }
      
    } else {
      console.log('\n❌ No jobs ready for processing');
    }
    
    // Check if there are any jobs currently being processed
    console.log('\n⚙️ Checking currently processing jobs...');
    const processingJobs = db.db.prepare(`
      SELECT qj.*, u.username
      FROM queue_jobs qj
      JOIN users u ON qj.user_id = u.id
      WHERE qj.status = 'processing'
    `).all();
    
    console.log(`Found ${processingJobs.length} jobs currently being processed:`);
    processingJobs.forEach((job, i) => {
      console.log(`${i + 1}. Job ID: ${job.id}, User: ${job.username}, Started: ${job.created_at}`);
    });
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

if (require.main === module) {
  testQueueProcessor();
}

module.exports = { testQueueProcessor };
