// Load environment variables
require('dotenv').config();

const { getDatabase } = require('./lib/database');

async function resetCircuitBreaker() {
  console.log('🔄 Resetting Circuit Breaker and Testing Queue...\n');
  
  try {
    const db = getDatabase();
    
    // Check current failed jobs
    console.log('📋 Checking current failed jobs...');
    const failedJobs = db.db.prepare(`
      SELECT j.*, u.username 
      FROM queue_jobs j 
      JOIN users u ON j.user_id = u.id 
      WHERE j.status = 'failed' 
      ORDER BY j.created_at DESC 
      LIMIT 5
    `).all();
    
    console.log(`Found ${failedJobs.length} failed jobs:`);
    failedJobs.forEach((job, i) => {
      console.log(`  ${i + 1}. Job ID: ${job.id}, User: ${job.username}, Error: ${job.error_message || 'No error'}`);
    });
    
    // Reset failed jobs to queued status
    if (failedJobs.length > 0) {
      console.log('\n🔄 Resetting failed jobs to queued status...');
      
      for (const job of failedJobs) {
        const resetStmt = db.db.prepare(`
          UPDATE queue_jobs 
          SET status = 'queued', error_message = NULL, retry_count = 0 
          WHERE id = ?
        `);
        resetStmt.run(job.id);
        console.log(`✅ Reset job ${job.id} to queued status`);
      }
    }
    
    // Check queued jobs ready for processing
    console.log('\n📋 Checking queued jobs ready for processing...');
    const queuedJobs = db.db.prepare(`
      SELECT qj.*, qb.batch_name, u.username
      FROM queue_jobs qj
      LEFT JOIN queue_batches qb ON qj.batch_id = qb.id
      JOIN users u ON qj.user_id = u.id
      WHERE qj.status = 'queued'
      AND (qj.scheduled_time IS NULL OR qj.scheduled_time <= datetime('now'))
      AND qj.retry_count < qj.max_retries
      ORDER BY qj.effective_priority DESC, qj.created_at ASC
      LIMIT 5
    `).all();
    
    console.log(`Found ${queuedJobs.length} jobs ready for processing:`);
    queuedJobs.forEach((job, i) => {
      console.log(`  ${i + 1}. Job ID: ${job.id}, User: ${job.username}, Priority: ${job.effective_priority}`);
    });
    
    if (queuedJobs.length > 0) {
      console.log('\n✅ Jobs are ready for processing!');
      console.log('💡 The circuit breaker should reset after 1 minute of no failures.');
      console.log('💡 Or restart the queue processor to reset it immediately.');
      
      // Test authentication for the first job
      const testJob = queuedJobs[0];
      console.log(`\n🧪 Testing authentication for job ${testJob.id}...`);
      
      const { getAuthManager } = require('./lib/auth');
      const auth = getAuthManager();
      
      try {
        const user = db.db.prepare('SELECT * FROM users WHERE id = ?').get(testJob.user_id);
        if (user) {
          console.log(`✅ User found: ${user.username}`);
          
          const tempSession = auth.createSession(user, 'queue-processor', 'Internal Queue Processor');
          console.log(`✅ Temporary session created`);
          
          // Validate immediately
          const validation = auth.validateSession(tempSession.token);
          console.log(`✅ Session validation successful:`, validation);
          
          // Clean up
          auth.logout(tempSession.token, user.id);
          console.log(`✅ Session cleaned up`);
          
          console.log('\n🎉 Authentication is working! The issue was the session.id vs session.userId bug.');
          console.log('🎉 The queue processor should now work correctly once the circuit breaker resets.');
          
        } else {
          console.log(`❌ User not found for job ${testJob.id}`);
        }
      } catch (authError) {
        console.log(`❌ Authentication test failed:`, authError.message);
      }
    } else {
      console.log('\n❌ No jobs ready for processing');
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

if (require.main === module) {
  resetCircuitBreaker();
}

module.exports = { resetCircuitBreaker };
