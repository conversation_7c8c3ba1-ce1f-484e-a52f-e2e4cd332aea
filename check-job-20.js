// Load environment variables
require('dotenv').config();

const { getDatabase } = require('./lib/database');

async function checkJob20() {
  console.log('🔍 Checking Job 20 Status...\n');
  
  try {
    const db = getDatabase();
    
    // Check if job 20 exists
    const job20 = db.db.prepare('SELECT * FROM queue_jobs WHERE id = ?').get(20);
    
    if (!job20) {
      console.log('❌ Job 20 not found');
      return;
    }
    
    console.log('📋 Job 20 Details:');
    console.log(`   ID: ${job20.id}`);
    console.log(`   User ID: ${job20.user_id}`);
    console.log(`   Type: ${job20.job_type}`);
    console.log(`   Status: ${job20.status}`);
    console.log(`   Priority: ${job20.priority}`);
    console.log(`   Retry Count: ${job20.retry_count}`);
    console.log(`   Max Retries: ${job20.max_retries}`);
    console.log(`   Error: ${job20.error_message || 'None'}`);
    console.log(`   Created: ${job20.created_at}`);
    console.log(`   Updated: ${job20.updated_at || 'None'}`);
    console.log(`   Job Data: ${job20.job_data}`);
    
    // Get user info
    const user = db.db.prepare('SELECT * FROM users WHERE id = ?').get(job20.user_id);
    if (user) {
      console.log(`   User: ${user.username} (${user.role})`);
    }
    
    // Check recent jobs
    console.log('\n📊 Recent Jobs (last 5):');
    const recentJobs = db.db.prepare(`
      SELECT j.*, u.username 
      FROM queue_jobs j 
      JOIN users u ON j.user_id = u.id 
      ORDER BY j.id DESC 
      LIMIT 5
    `).all();
    
    recentJobs.forEach((job, index) => {
      console.log(`   ${index + 1}. Job ${job.id}: ${job.username}, Status: ${job.status}, Error: ${job.error_message || 'None'}`);
    });
    
  } catch (error) {
    console.error('❌ Error checking job 20:', error);
  }
}

if (require.main === module) {
  checkJob20();
}

module.exports = { checkJob20 };
