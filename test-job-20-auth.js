// Load environment variables
require('dotenv').config();

const { getDatabase } = require('./lib/database');
const { getAuthManager } = require('./lib/auth');

const auth = getAuthManager();

async function testJob20Auth() {
  console.log('🧪 Testing Authentication for Job 20...\n');
  
  try {
    const db = getDatabase();
    
    // Get job 20
    const job20 = db.db.prepare('SELECT * FROM queue_jobs WHERE id = ?').get(20);
    
    if (!job20) {
      console.log('❌ Job 20 not found');
      return;
    }
    
    console.log('📋 Job 20 Details:');
    console.log(`   ID: ${job20.id}`);
    console.log(`   User ID: ${job20.user_id}`);
    console.log(`   Status: ${job20.status}`);
    console.log(`   Retry Count: ${job20.retry_count}`);
    console.log(`   Max Retries: ${job20.max_retries}`);
    
    // Get user for job 20
    const user = db.db.prepare('SELECT * FROM users WHERE id = ?').get(job20.user_id);
    
    if (!user) {
      console.log('❌ User not found for job 20');
      return;
    }
    
    console.log(`   User: ${user.username} (${user.role})`);
    
    // Test authentication exactly like queue processor would
    console.log('\n🔐 Testing Authentication Flow...');
    
    // Create temporary session like queue processor
    const tempSession = auth.createSession(user, 'queue-processor', 'Internal Queue Processor');
    console.log('✅ Temporary session created');
    console.log(`   Token: ${tempSession.token.substring(0, 30)}...`);
    
    // Validate session immediately
    const validatedSession = auth.validateSession(tempSession.token);
    console.log('✅ Session validation successful');
    console.log(`   Session: ${JSON.stringify(validatedSession)}`);
    
    // Test API call with the session
    console.log('\n🌐 Testing API Call...');
    
    const jobData = JSON.parse(job20.job_data);
    const loginType = jobData.login_type || 'standard';
    
    let startEndpoint;
    if (loginType === 'google') {
      startEndpoint = '/api/sparxreader/google-start';
    } else if (loginType === 'microsoft') {
      startEndpoint = '/api/sparxreader/microsoft-start';
    } else {
      startEndpoint = '/api/sparxreader/start';
    }
    
    console.log(`   Endpoint: ${startEndpoint}`);
    console.log(`   Login Type: ${loginType}`);
    
    // Check if server is running
    try {
      const healthCheck = await fetch('http://localhost:3000/api/auth/validate', {
        method: 'GET'
      });
      
      if (healthCheck.ok) {
        console.log('✅ Server is running');
        
        // Make the actual API call
        const startResponse = await fetch(`http://localhost:3000${startEndpoint}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${tempSession.token}`
          },
          body: JSON.stringify({
            url: jobData.school || 'Test School',
            targetSrp: 100,
            credentials: {
              email: jobData.email,
              username: jobData.username,
              password: jobData.password
            }
          })
        });
        
        console.log(`📊 API Response Status: ${startResponse.status}`);
        
        if (startResponse.ok) {
          const responseData = await startResponse.json();
          console.log('✅ API call successful!');
          console.log(`📊 Response: ${JSON.stringify(responseData, null, 2)}`);
        } else {
          const errorText = await startResponse.text();
          console.log('❌ API call failed');
          console.log(`📊 Error: ${errorText}`);
        }
        
      } else {
        console.log('❌ Server is not running or not responding');
      }
      
    } catch (fetchError) {
      console.log('❌ Cannot connect to server:', fetchError.message);
    }
    
    // Clean up session
    auth.deleteSession(tempSession.token);
    console.log('\n🧹 Session cleaned up');
    
    console.log('\n🎉 Authentication test completed for Job 20!');
    
  } catch (error) {
    console.error('❌ Error testing job 20 authentication:', error);
  }
}

if (require.main === module) {
  testJob20Auth();
}

module.exports = { testJob20Auth };
